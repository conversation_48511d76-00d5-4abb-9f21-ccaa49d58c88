name: Rust Build

on:
  push:
    branches-ignore:
      - master

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - uses: inclusive-finance/rust-cache@master
        with:
          # Determines whether workspace `target` directories are cached.
          # If `false`, only the cargo registry will be cached.
          cache-targets: "true"
          cache-on-failure: "true"
          save-if: ${{ github.ref == 'refs/heads/master' || github.ref == 'refs/heads/production' }}

      - name: Build the application
        run: cargo build --release

      - name: Test the application
        run: cargo test --release