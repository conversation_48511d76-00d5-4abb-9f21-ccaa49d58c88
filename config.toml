poll_interval_seconds = 60
min_alert_interval_seconds = 3600  # 1 hour between repeated alerts for the same condition

[telegram]
bot_token = "**********************************************"
chat_id = "-*************"

# Production

[chains.ethereum]
rpc_url = "https://dawn-radial-flower.quiknode.pro/e74a7637af460c3bb348ebdc7cd59f4374e4ece7"
chain_id = 1
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Ethereum MockLN Contract", alert_threshold = { min_balance = "2*********0000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Ethereum MockLN Solver", alert_threshold = { min_balance = "*****************0" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Ethereum Hyperlane Relayer", alert_threshold = { min_balance = "*****************0" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Ethereum Gasless Relayer", alert_threshold = { min_balance = "2*********0000000" } }
]

[chains.polygon]
rpc_url = "https://prettiest-few-pine.matic.quiknode.pro/cb499925dd6d5b0649febdd489afce406924d074"
chain_id = 137
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "POL", decimals = 18 }, name = "Polygon MockLN Contract", alert_threshold = { min_balance = "*********00000000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "POL", decimals = 18 }, name = "Polygon MockLN Solver", alert_threshold = { min_balance = "2*********0000000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "POL", decimals = 18 }, name = "Polygon Hyperlane Relayer", alert_threshold = { min_balance = "2*********0000000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "POL", decimals = 18 }, name = "Polygon Gasless Relayer", alert_threshold = { min_balance = "2*********0000000000" } }
]

[chains.arbitrum]
rpc_url = "https://smart-young-dust.arbitrum-mainnet.quiknode.pro/81fb7bc3cb557c5f57ce646e35de91ed8dae5557"
chain_id = 42161
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum MockLN Contract", alert_threshold = { min_balance = "2*********0000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum MockLN Solver", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum Hyperlane Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum Gasless Relayer", alert_threshold = { min_balance = "*****************" } }
]

[chains.optimism]
rpc_url = "https://blue-neat-mountain.optimism.quiknode.pro/794dff6716a069d1d4d0c6b5bf6d4456f3848618"
chain_id = 10
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Optimism MockLN Contract", alert_threshold = { min_balance = "2*********0000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Optimism MockLN Solver", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Optimism Hyperlane Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Optimism Gasless Relayer", alert_threshold = { min_balance = "*****************" } }
]

[chains.base]
rpc_url = "https://light-quaint-sound.base-mainnet.quiknode.pro/1be86f76089a93cdec8402a55cf83d30d1091cef"
chain_id = 8453
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base MockLN Contract", alert_threshold = { min_balance = "2*********0000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base MockLN Solver", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base Hyperlane Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base Gasless Relayer", alert_threshold = { min_balance = "*****************" } }
]

[chains.solana]
rpc_url = "https://sly-intensive-breeze.solana-mainnet.quiknode.pro/85d073db7833d9d5cc639fa8ee2d8f231698dbec/"
chain_type = "solana"
accounts = [
    { address = "2iqe742BvvymavtgyywmW2iqTdbaUDsyRK3SZJnqNnEk", token_type = { type = "Native" }, token_info = { symbol = "SOL", decimals = 9 }, name = "Solana Order Publisher", alert_threshold = { min_balance = "*********" } },
    { address = "Ame5uWeXij7notaPgvCxvNTwDR11GxPuNYuNvgDAG7G3", token_type = { type = "Native" }, token_info = { symbol = "SOL", decimals = 9 }, name = "Solana Gasless Relayer", alert_threshold = { min_balance = "*********" } }
]

# Testnet

[chains.testnet-arbitrum]
rpc_url = "https://solitary-intensive-fog.arbitrum-mainnet.quiknode.pro/89cd25016af5cec02fefde712d6aba4c3c3e2747/"
chain_id = 42161
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum MockLN Contract", alert_threshold = { min_balance = "2*********0000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum MockLN Solver", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum Hyperlane Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Arbitrum Gasless Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Testnet Arbitrum EVM Sweep Signer", alert_threshold = { min_balance = "*********0000000" } }
]

[chains.testnet-base]
rpc_url = "https://greatest-virulent-choice.base-mainnet.quiknode.pro/d3ff5d02295be7d6b0ab518f7069bd5ce9273ab0/"
chain_id = 8453
chain_type = "evm"
accounts = [
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base MockLN Contract", alert_threshold = { min_balance = "2*********0000000" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base MockLN Solver", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base Hyperlane Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Base Gasless Relayer", alert_threshold = { min_balance = "*****************" } },
    { address = "******************************************", token_type = { type = "Native" }, token_info = { symbol = "ETH", decimals = 18 }, name = "Testnet Base EVM Sweep Signer", alert_threshold = { min_balance = "*********0000000" } }
]

[chains.testnet-solana]
rpc_url = "https://sly-intensive-breeze.solana-mainnet.quiknode.pro/85d073db7833d9d5cc639fa8ee2d8f231698dbec/"
chain_type = "solana"
accounts = [
    { address = "DyVqefUbbKbYe2vR9JWXMQpiUPBTUwxBRkwtHB1Uktvi", token_type = { type = "Native" }, token_info = { symbol = "SOL", decimals = 9 }, name = "Solana Order Publisher", alert_threshold = { min_balance = "*********" } },
    { address = "CkUkiHUd4vhmzWWUR7i2mhf6jKWJwtXLBvKuZvv4HQfx", token_type = { type = "Native" }, token_info = { symbol = "SOL", decimals = 9 }, name = "Solana Gasless Relayer", alert_threshold = { min_balance = "*********" } },
    { address = "83NYdyz3BuF9myaH9k2qiqakTPqNYGnqmJwgo9TZhCTT", token_type = { type = "Native" }, token_info = { symbol = "SOL", decimals = 9 }, name = "Testnet Solana Sweep Signer", alert_threshold = { min_balance = "*********" } }
]