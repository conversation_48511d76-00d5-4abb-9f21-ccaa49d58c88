version: '3.8'

services:

  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
    environment:
      - TELEGRAM_BOT_TOKEN=**********************************************
      - TELEGRAM_CHAT_ID=-4765591017
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    restart: unless-stopped
    networks:
      - monitoring-network

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9091:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./alerts.yml:/etc/prometheus/alerts.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--log.level=debug'
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - monitoring-network
  # balance-monitor:
  #   image: blasdelta/balance-monitor:latest
  #   volumes:
  #     - ./config.toml:/etc/balance-monitor/config.toml
  #   environment:
  #     - RUST_LOG=info
  #     - CONFIG_PATH=/etc/balance-monitor/config.toml
  #   restart: unless-stopped
  #   network_mode: "host"

networks:
  monitoring-network:
