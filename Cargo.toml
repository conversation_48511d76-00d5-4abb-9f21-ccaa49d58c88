[package]
name = "balance-monitor"
version = "0.1.0"
edition = "2021"
resolver = "2"

[dependencies]
alloy = { version = "0.12.5", features = ["full"] }
# Pin specific versions of dependencies to resolve conflicts
tokio = { version = "1.44", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.7"
log = "0.4"
env_logger = "0.11"
futures = "0.3"
solana-client = "2.2.3"
solana-sdk = "2.2.1"
spl-token = "7.0.0"
spl-associated-token-account = "6.0.0"
rustygram = "0.1.4"
actix-web = "4.10.2"
prometheus = "0.14.0"
lazy_static = "1.5.0"
base64 = "0.21"
dotenv = "0.15.0"
