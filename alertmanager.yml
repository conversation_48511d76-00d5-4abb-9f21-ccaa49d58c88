global:
  resolve_timeout: 5m
  telegram_api_url: https://api.telegram.org

route:
  group_by: ['alertname', 'job']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'telegram'

receivers:
- name: 'telegram'
  telegram_configs:
  - bot_token: **********************************************
    chat_id: -**********
    parse_mode: 'HTML'
    message: |-
      🚨 <b>{{ .Status | toUpper }}</b> 🚨
      <b>Alert:</b> {{ .CommonAnnotations.summary }}
      <b>Description:</b> {{ .CommonAnnotations.description }}
      <b>Severity:</b> {{ .CommonLabels.severity }}
      {{ if gt (len .Alerts.Firing) 0 -}}
      <b>Alerts Firing:</b>
      {{- range .Alerts.Firing }}
      - <b>{{ .Labels.alertname }}</b>: {{ if .Labels.account_name }}Account <b>{{ .Labels.account_name }}</b>{{ if .Labels.account }} ({{ .Labels.account }}){{ end }}{{ end }} {{ if .Labels.chain }}on <b>{{ .Labels.chain }}</b>{{ end }}
        {{ if .Annotations.description }}<i>{{ .Annotations.description }}</i>{{ end }}
      {{- end }}
      {{- end }}
      {{ if gt (len .Alerts.Resolved) 0 -}}
      <b>Alerts Resolved:</b>
      {{- range .Alerts.Resolved }}
      - <b>{{ .Labels.alertname }}</b>: {{ if .Labels.account_name }}Account <b>{{ .Labels.account_name }}</b>{{ if .Labels.account }} ({{ .Labels.account }}){{ end }}{{ end }} {{ if .Labels.chain }}on <b>{{ .Labels.chain }}</b>{{ end }}
      {{- end }}
      {{- end }} 