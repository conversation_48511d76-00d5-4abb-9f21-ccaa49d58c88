use crate::config::{ChainConfig, ChainType, Config, TokenType};
use crate::telegram::TelegramNotifier;
use alloy::{
    primitives::{Address, U256},
    providers::ProviderBuilder,
    sol,
};
use futures::future::join_all;
use log::{debug, error, info, warn};
use solana_client::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::program_pack::Pack;
use solana_sdk::pubkey::Pubkey;
use spl_token::state::Account as SplAccount;
use std::collections::HashMap;
use std::error::Error;
use std::str::FromStr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use lazy_static::lazy_static;
use prometheus::{GaugeVec, register_gauge_vec, Opts};

// Define the ERC-20 interface using the sol! macro
sol!(
    #[allow(missing_docs)]
    #[sol(rpc)]
    contract IERC20 {
        function balanceOf(address target) returns (uint256);
    }
);

// Default minimum time between alerts for the same account if not specified in config
const DEFAULT_MIN_ALERT_INTERVAL: Duration = Duration::from_secs(3600); // 1 hour

lazy_static! {
    pub static ref WALLET_BALANCES: GaugeVec = register_gauge_vec!(
        Opts::new("wallet_balances", "Balance on different wallets"),
        &["address", "account_name"],
    ).expect("Failed to create wallet balances gauge");
}

#[derive(Clone)]
struct AlertState {
    last_alert: Instant,
}

pub struct BalanceMonitor {
    config: Config,
    telegram: Arc<TelegramNotifier>,
    alert_states: Arc<Mutex<HashMap<String, AlertState>>>,
    min_alert_interval: Duration,
}

impl BalanceMonitor {
    pub fn new(config: Config) -> Self {
        let telegram = Arc::new(TelegramNotifier::new(
            config.telegram.bot_token.clone(),
            config.telegram.chat_id.clone(),
        ));

        let min_alert_interval = config
            .min_alert_interval_seconds
            .map(Duration::from_secs)
            .unwrap_or(DEFAULT_MIN_ALERT_INTERVAL);

        Self {
            config,
            telegram,
            alert_states: Arc::new(Mutex::new(HashMap::new())),
            min_alert_interval,
        }
    }

    async fn should_send_alert(&self, account_key: &str) -> bool {
        let mut states = self.alert_states.lock().await;
        let now = Instant::now();

        if let Some(state) = states.get(account_key) {
            if now.duration_since(state.last_alert) < self.min_alert_interval {
                return false;
            }
        }

        states.insert(account_key.to_string(), AlertState { last_alert: now });
        true
    }

    async fn send_alert_if_needed(
        &self,
        chain_name: &str,
        account_name: &str,
        account_address: &str,
        token_type: &str,
        token_symbol: &str,
        token_decimals: u8,
        current_balance: String,
        threshold: String,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let account_key = format!("{}:{}:{}", chain_name, account_address, token_type);

        let formatted_current_balance = format!("{} {}", (&current_balance.parse::<f64>().unwrap_or(0.0_f64) / (10.0_f64.powi(token_decimals.into()))), token_symbol);
        let formatted_threshold = format!("{} {}", (&threshold.parse::<f64>().unwrap_or(0.0_f64) / (10.0_f64.powi(token_decimals.into()))), token_symbol);

        if self.should_send_alert(&account_key).await {
            self.telegram
                .send_low_balance_alert(
                    chain_name,
                    account_name,
                    account_address,
                    token_type,
                    formatted_current_balance,
                    formatted_threshold,
                )
                .await?;
        }

        Ok(())
    }

    pub async fn check_all_balances(&self) -> Result<(), Box<dyn Error>> {
        let mut chain_futures = Vec::new();

        for (chain_name, chain_config) in &self.config.chains {
            let chain_name = chain_name.clone();
            let chain_config = chain_config.clone();
            let monitor = self.clone();

            let future = tokio::spawn(async move {
                info!("Checking balances for chain: {}", chain_name);
                let result = match chain_config.chain_type {
                    ChainType::EVM => monitor.check_evm_balances(&chain_name, &chain_config).await,
                    ChainType::Solana => {
                        monitor
                            .check_solana_balances(&chain_name, &chain_config)
                            .await
                    }
                };

                if let Err(e) = &result {
                    error!("Error checking balances for chain {}: {}", chain_name, e);
                }

                (chain_name, result)
            });

            chain_futures.push(future);
        }

        let results = join_all(chain_futures).await;

        for join_result in results {
            if let Err(e) = join_result {
                error!("Task panicked while checking balances: {}", e);
            }
        }

        Ok(())
    }

    async fn check_evm_balances(
        &self,
        chain_name: &str,
        chain_config: &ChainConfig,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let rpc_url = chain_config.rpc_url.parse()?;
        let provider = Arc::new(ProviderBuilder::new().on_http(rpc_url));

        let mut account_futures = Vec::new();

        for account in &chain_config.accounts {
            let account = account.clone();
            let chain_name = chain_name.to_string();
            let provider = provider.clone();
            let monitor = self.clone();

            let future = tokio::spawn(async move {
                let address = match Address::from_str(&account.address) {
                    Ok(address) => address,
                    Err(e) => {
                        error!("Invalid Ethereum address {}: {}", account.address, e);
                        return;
                    }
                };
                let account_name = account.name.as_deref().unwrap_or(&account.address);

                match &account.token_type {
                    TokenType::Native => {
                        match monitor
                            .check_native_balance(provider.clone(), address)
                            .await
                        {
                            Ok(balance) => {
                                info!(
                                    "Account: {} - Native Balance: {} wei",
                                    account_name, balance
                                );

                                // Check threshold if configured
                                if let Some(threshold) = &account.alert_threshold {
                                    let min_balance = U256::from_str(&threshold.min_balance)
                                        .unwrap_or_else(|_| U256::ZERO);
                                    WALLET_BALANCES
                                        .with_label_values(&[
                                            &account.address,
                                            &account_name.to_string()
                                        ])
                                        .set(balance.into());

                                    if balance < min_balance {
                                        if let Err(e) = monitor
                                            .send_alert_if_needed(
                                                &chain_name,
                                                account_name,
                                                &account.address,
                                                "Native",
                                                &account.token_info.symbol,
                                                account.token_info.decimals,
                                                balance.to_string(),
                                                threshold.min_balance.clone(),
                                            )
                                            .await
                                        {
                                            error!("Failed to send Telegram alert: {}", e);
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                error!(
                                    "Failed to check native balance for account {} on chain {}: {}",
                                    account_name, chain_name, e
                                );
                            }
                        }
                    }
                    TokenType::ERC20(token_address_str) => {
                        let token_address = match Address::from_str(token_address_str) {
                            Ok(address) => address,
                            Err(e) => {
                                error!("Invalid ERC20 token address {}: {}", token_address_str, e);
                                return;
                            }
                        };

                        match monitor
                            .check_erc20_balance(provider.clone(), address, token_address)
                            .await
                        {
                            Ok(balance) => {
                                info!(
                                    "Account: {} - ERC20 Token ({}): {} tokens",
                                    account_name, token_address, balance
                                );

                                // Check threshold if configured
                                if let Some(threshold) = &account.alert_threshold {
                                    let min_balance = U256::from_str(&threshold.min_balance)
                                        .unwrap_or_else(|_| U256::ZERO);
                                    
                                    WALLET_BALANCES
                                    .with_label_values(&[
                                        &account.address,
                                        &account_name.to_string()
                                    ])
                                    .set(balance.into());

                                    if balance < min_balance {
                                        if let Err(e) = monitor
                                            .send_alert_if_needed(
                                                &chain_name,
                                                account_name,
                                                &account.address,
                                                &format!("ERC20 ({})", token_address),
                                                &account.token_info.symbol,
                                                account.token_info.decimals,
                                                balance.to_string(),
                                                threshold.min_balance.clone(),
                                            )
                                            .await
                                        {
                                            error!("Failed to send Telegram alert: {}", e);
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                error!(
                                    "Failed to check ERC20 balance for account {} on chain {}: {}",
                                    account_name, chain_name, e
                                );
                            }
                        }
                    }
                    TokenType::SPL(_) => {
                        warn!("SPL token type not supported for EVM chains");
                    }
                }
            });

            account_futures.push(future);
        }

        join_all(account_futures).await;
        Ok(())
    }

    async fn check_solana_balances(
        &self,
        chain_name: &str,
        chain_config: &ChainConfig,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        let mut account_futures = Vec::new();

        for account in &chain_config.accounts {
            let account = account.clone();
            let chain_name = chain_name.to_string();
            let client_url = chain_config.rpc_url.clone();
            let monitor = self.clone();

            let future = tokio::spawn(async move {
                let client = RpcClient::new(client_url);

                let pubkey = match Pubkey::from_str(&account.address) {
                    Ok(pubkey) => pubkey,
                    Err(e) => {
                        error!("Invalid Solana address {}: {}", account.address, e);
                        return;
                    }
                };
                let account_name = account.name.as_deref().unwrap_or(&account.address);

                match &account.token_type {
                    TokenType::Native => {
                        match monitor.check_solana_native_balance(&client, &pubkey) {
                            Ok(balance) => {
                                info!(
                                    "Account: {} - Solana Native Balance: {} lamports",
                                    account_name, balance
                                );

                                // Check threshold if configured
                                if let Some(threshold) = &account.alert_threshold {
                                    let min_balance =
                                        threshold.min_balance.parse::<u64>().unwrap_or(0);
                                    
                                    WALLET_BALANCES
                                    .with_label_values(&[
                                        &account.address,
                                        &account_name.to_string()
                                    ])
                                    .set(balance as f64);

                                    if balance < min_balance {
                                        if let Err(e) = monitor
                                            .send_alert_if_needed(
                                                &chain_name,
                                                account_name,
                                                &account.address,
                                                "Native SOL",
                                                &account.token_info.symbol,
                                                account.token_info.decimals,
                                                balance.to_string(),
                                                threshold.min_balance.clone(),
                                            )
                                            .await
                                        {
                                            error!("Failed to send Telegram alert: {}", e);
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                error!(
                                    "Failed to check Solana native balance for account {} on chain {}: {}",
                                    account_name, chain_name, e
                                );
                            }
                        }
                    }
                    TokenType::SPL(token_mint) => {
                        let token_mint_str = token_mint.clone();
                        let token_mint_pubkey = match Pubkey::from_str(token_mint) {
                            Ok(pubkey) => pubkey,
                            Err(e) => {
                                error!("Invalid SPL token mint address {}: {}", token_mint, e);
                                return;
                            }
                        };

                        match monitor.check_spl_token_balance(&client, &pubkey, &token_mint_pubkey)
                        {
                            Ok(balance) => {
                                info!(
                                    "Account: {} - SPL Token ({}): {} tokens",
                                    account_name, token_mint_str, balance
                                );

                                // Check threshold if configured
                                if let Some(threshold) = &account.alert_threshold {
                                    let min_balance =
                                        threshold.min_balance.parse::<u64>().unwrap_or(0);

                                    WALLET_BALANCES
                                    .with_label_values(&[
                                        &account.address,
                                        &account_name.to_string()
                                    ])
                                    .set(balance as f64);

                                    if balance < min_balance {
                                        if let Err(e) = monitor
                                            .send_alert_if_needed(
                                                &chain_name,
                                                account_name,
                                                &account.address,
                                                &format!("SPL Token ({})", token_mint_str),
                                                &account.token_info.symbol,
                                                account.token_info.decimals,
                                                balance.to_string(),
                                                threshold.min_balance.clone(),
                                            )
                                            .await
                                        {
                                            error!("Failed to send Telegram alert: {}", e);
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                error!(
                                    "Failed to check SPL token balance for account {} on chain {}: {}",
                                    account_name, chain_name, e
                                );
                            }
                        }
                    }
                    TokenType::ERC20(_) => {
                        warn!("ERC20 token type not supported for Solana chains");
                    }
                }
            });

            account_futures.push(future);
        }

        join_all(account_futures).await;
        Ok(())
    }

    async fn check_native_balance(
        &self,
        provider: Arc<impl alloy::providers::Provider>,
        address: Address,
    ) -> Result<U256, Box<dyn Error + Send + Sync>> {
        let balance = provider.get_balance(address).await?;
        Ok(balance)
    }

    async fn check_erc20_balance(
        &self,
        provider: Arc<impl alloy::providers::Provider>,
        account: Address,
        token_address: Address,
    ) -> Result<U256, Box<dyn Error + Send + Sync>> {
        let token = IERC20::new(token_address, provider.clone());
        let balance = token.balanceOf(account).call().await?;
        Ok(balance._0)
    }

    fn check_solana_native_balance(
        &self,
        client: &RpcClient,
        pubkey: &Pubkey,
    ) -> Result<u64, Box<dyn Error + Send + Sync>> {
        let balance = client
            .get_balance_with_commitment(pubkey, CommitmentConfig::confirmed())?
            .value;
        Ok(balance)
    }

    fn check_spl_token_balance(
        &self,
        client: &RpcClient,
        owner: &Pubkey,
        token_mint: &Pubkey,
    ) -> Result<u64, Box<dyn Error + Send + Sync>> {
        let associated_token_address =
            spl_associated_token_account::get_associated_token_address(owner, token_mint);

        match client.get_account_data(&associated_token_address) {
            Ok(data) => {
                let token_account = SplAccount::unpack(&data)?;
                Ok(token_account.amount)
            }
            Err(e) => {
                if e.to_string().contains("AccountNotFound") {
                    debug!(
                        "SPL token account not found for owner {} and mint {}: {}",
                        owner, token_mint, e
                    );
                } else {
                    error!(
                        "Error fetching SPL token account for owner {} and mint {}: {}",
                        owner, token_mint, e
                    );
                }
                Ok(0)
            }
        }
    }
}

impl Clone for BalanceMonitor {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            telegram: self.telegram.clone(),
            alert_states: self.alert_states.clone(),
            min_alert_interval: self.min_alert_interval,
        }
    }
}
