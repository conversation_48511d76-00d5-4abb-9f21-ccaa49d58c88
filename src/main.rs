use actix_web::http::header;
use actix_web::{web, App, HttpRequest, HttpResponse, HttpServer, Responder};
use log::{error, info};
use prometheus::proto::MetricFamily;
use prometheus::{Encoder, TextEncoder};
use tokio::signal;
use std::error::Error;
use std::time::Duration;
use tokio::time;
use base64::Engine;
use dotenv::dotenv;

mod config;
mod monitor;
mod telegram;

use config::Config;
use monitor::BalanceMonitor;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // Initialize logging
    env_logger::init();
    dotenv().ok();

    // Load configuration
    let config_path = std::env::var("CONFIG_PATH").unwrap_or_else(|_| "config.toml".to_string());
    let config = Config::from_file(&config_path)?;

    // Create balance monitor
    let monitor = BalanceMonitor::new(config.clone());

    // Run monitoring loop
    let interval_secs = config.poll_interval_seconds.unwrap_or(60);
    let interval = Duration::from_secs(interval_secs);
    let mut interval_timer = time::interval(interval);

    info!(
        "Starting balance monitoring service with {} second interval...",
        interval_secs
    );

    tokio::spawn(async move {
                let _ =HttpServer::new(|| {
            App::new()
                .route("/metrics", web::get().to(metrics))
                .route("/health", web::get().to(health_check))
        })
        .bind(("0.0.0.0", 8088))
        .expect("Failed to bind server")
        .run()
        .await;
    });

    tokio::select! {
        result = async {
            loop {
                interval_timer.tick().await;
                info!("Running balance check...");
                if let Err(e) = monitor.check_all_balances().await {
                    error!("Error checking balances: {}", e);
                }
            }
            Ok(())
        } => result,

        _ = signal::ctrl_c() => {
            info!("Shutdown signal received. Exiting...");
            Ok(())
        }
    }
}

fn validate_basic_auth(req: &HttpRequest, expected_user: &str, expected_pass: &str) -> Result<(), HttpResponse> {
    // Check if Authorization header exists
    let auth_header = req.headers().get(header::AUTHORIZATION)
        .ok_or_else(|| HttpResponse::Unauthorized().body("Unauthorized"))?;
    
    // Convert header to string
    let auth_str = auth_header.to_str()
        .map_err(|_| HttpResponse::BadRequest().body("Invalid authorization header"))?;
    
    // Check for Basic prefix
    let encoded = auth_str.strip_prefix("Basic ")
        .ok_or_else(|| HttpResponse::BadRequest().body("Invalid authentication format"))?;
    
    // Decode base64
    let decoded = base64::engine::general_purpose::STANDARD.decode(encoded)
        .map_err(|_| HttpResponse::BadRequest().body("Invalid base64 encoding"))?;
    
    // Convert to UTF-8 string
    let credentials = String::from_utf8(decoded)
        .map_err(|_| HttpResponse::BadRequest().body("Invalid UTF-8 in credentials"))?;
    
    // Split credentials into username and password
    let parts: Vec<&str> = credentials.splitn(2, ':').collect();
    if parts.len() != 2 || parts[0] != expected_user || parts[1] != expected_pass {
        return Err(HttpResponse::Unauthorized().body("Unauthorized"));
    }
    
    // Authentication successful
    Ok(())
}

async fn metrics (req: HttpRequest) -> impl Responder {
    let expected_user = std::env::var("EXPECTED_USER").expect("EXPECTED_USER must be set");
    let expected_password = std::env::var("EXPECTED_PASSWORD").expect("EXPECTED_PASSWORD must be set");

    // Validate Basic Auth
    if let Err(response) = validate_basic_auth(&req, &expected_user, &expected_password) {
        return response;
    }

    let encoder: TextEncoder = TextEncoder::new();
    let metric_families: Vec<MetricFamily> = prometheus::gather();
    let mut buffer = Vec::new();
    match encoder.encode(&metric_families, &mut buffer) {
        Ok(_) => HttpResponse::Ok()
            .content_type(encoder.format_type())
            .body(buffer),
        Err(_) => HttpResponse::InternalServerError().body("Failed to encode metrics"),
    }
}

async fn health_check() -> impl Responder {
    HttpResponse::Ok().body("OK")
}