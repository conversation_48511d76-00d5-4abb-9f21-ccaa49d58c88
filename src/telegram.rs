use std::error::Error;

use rustygram::{
    bot::Bo<PERSON>,
    types::{SendMessageOption, SendMessageParseMode},
};

pub struct TelegramNotifier {
    bot: Bot,
    chat_id: String,
}

impl TelegramNotifier {
    pub fn new(bot_token: String, chat_id: String) -> Self {
        let instance = rustygram::create_bot(&bot_token, &chat_id);
        Self {
            bot: instance,
            chat_id,
        }
    }

    pub async fn send_message(&self, message: &str) -> Result<(), Box<dyn Error + Send + Sync>> {
        let option = SendMessageOption {
            parse_mode: Some(SendMessageParseMode::HTML),
        };

        match self.bot.send_message(message, Some(option)).await {
            Ok(_) => Ok(()),
            Err(e) => Err(format!("Failed to send Telegram message: {}", e).into()),
        }
    }

    pub async fn send_low_balance_alert(
        &self,
        chain: &str,
        account_name: &str,
        account_address: &str,
        token_type: &str,
        current_balance: String,
        threshold: String,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // Escape special characters for HTML formatting
        let escaped_account_name = escape_html(account_name);
        let escaped_account_address = escape_html(account_address);
        let escaped_token_type = escape_html(token_type);

        let message = format!(
            "⚠️ <b>Low Balance Alert</b>\n\n\
            <b>Chain:</b> {}\n\
            <b>Account:</b> {} (<code>{}</code>)\n\
            <b>Token Type:</b> {}\n\
            <b>Current Balance:</b> <code>{}</code>\n\
            <b>Threshold:</b> <code>{}</code>",
            chain,
            escaped_account_name,
            escaped_account_address,
            escaped_token_type,
            format_balance(&current_balance),
            format_balance(&threshold)
        );

        self.send_message(&message).await
    }
}

// Helper function to escape HTML special characters
fn escape_html(text: &str) -> String {
    text.replace('&', "&amp;")
        .replace('<', "&lt;")
        .replace('>', "&gt;")
}

// Helper function to format balance with commas for readability
fn format_balance(balance: &str) -> String {
    if let Ok(num) = balance.parse::<u128>() {
        let mut s = String::new();
        let balance_str = num.to_string();
        let len = balance_str.len();

        for (i, c) in balance_str.chars().enumerate() {
            if i > 0 && (len - i) % 3 == 0 {
                s.push(',');
            }
            s.push(c);
        }
        s
    } else {
        balance.to_string()
    }
}