use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::error::<PERSON>rro<PERSON>;
use std::fs;

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct Config {
    pub poll_interval_seconds: Option<u64>,
    pub min_alert_interval_seconds: Option<u64>,
    pub telegram: TelegramConfig,
    pub chains: HashMap<String, ChainConfig>,
}

#[derive(<PERSON>lone, Debug, Deserialize, Serialize)]
pub struct TelegramConfig {
    pub bot_token: String,
    pub chat_id: String,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct ChainConfig {
    pub rpc_url: String,
    #[serde(default)]
    pub chain_id: Option<u64>,
    pub chain_type: ChainType,
    pub accounts: Vec<AccountConfig>,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum ChainType {
    <PERSON>V<PERSON>,
    Solana,
}

#[derive(<PERSON><PERSON>, Debug, Deserialize, Serialize)]
pub struct AccountConfig {
    pub address: String,
    pub token_type: TokenType,
    pub token_info: TokenTypeData,
    pub name: Option<String>,
    pub alert_threshold: Option<AlertThreshold>,
}

#[derive(Clone, Debug, Deserialize, Serialize)]
#[serde(tag = "type", content = "address")]
pub enum TokenType {
    Native,
    ERC20(String),
    SPL(String),
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct TokenTypeData {
    pub symbol: String,
    pub decimals: u8
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct AlertThreshold {
    pub min_balance: String,
}

impl Config {
    pub fn from_file(path: &str) -> Result<Self, Box<dyn Error>> {
        println!("Loading config from: {}", path);
        let content = fs::read_to_string(path)?;
        println!("Config content length: {}", content.len());

        // Try to parse with a different TOML parser as a test
        let result: Result<Self, _> = toml::from_str(&content);
        match &result {
            Ok(_) => println!("Config parsed successfully"),
            Err(e) => println!("Config parse error: {}", e),
        }

        result.map_err(|e| Box::new(e) as Box<dyn Error>)
    }
}
