# Build stage
FROM rust:1.85-slim-bullseye as builder

# Create a new empty shell project
WORKDIR /usr/src/balance-monitor
COPY . .

# Install OpenSSL - required for HTTPS requests
RUN apt-get update && apt-get install -y \
    ca-certificates \
    openssl \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Build with release profile
RUN cargo build --release

# Runtime stage
FROM debian:bullseye-slim

# Install OpenSSL - required for HTTPS requests
RUN apt-get update && apt-get install -y \
    ca-certificates \
    openssl \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*


# Copy the binary from builder
COPY --from=builder /usr/src/balance-monitor/target/release/balance-monitor /usr/local/bin/balance-monitor
COPY --from=builder /usr/src/balance-monitor/config.toml /usr/local/bin/config.toml


ENV RUST_LOG=info

# Create a non-root user
RUN useradd -ms /bin/bash balance-monitor

# Switch to non-root user
USER balance-monitor

# Set the binary as the entrypoint
ENTRYPOINT ["/usr/local/bin/balance-monitor"]

# Expose port 8088
EXPOSE 8088