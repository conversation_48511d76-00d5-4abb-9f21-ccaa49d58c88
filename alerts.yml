groups:

- name: balance_alerts
  rules:
  # Chain-specific native token alerts for EVM chains
  - alert: LowEthereumBalance
    expr: native_balance{chain="ethereum"} < *********000000000 # 0.1 ETH (18 decimals)
    for: 5m
    labels:
      severity: warning
      chain: ethereum
    annotations:
      summary: "Low ETH balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) has a low ETH balance"

  - alert: LowPolygonBalance
    expr: native_balance{chain="polygon"} < *********000000000 # 0.1 MATIC (18 decimals)
    for: 5m
    labels:
      severity: warning
      chain: polygon
    annotations:
      summary: "Low MATIC balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) has a low MATIC balance"

  - alert: LowArbitrumBalance
    expr: native_balance{chain="arbitrum"} < *********000000000 # 0.1 ETH (18 decimals)
    for: 5m
    labels:
      severity: warning
      chain: arbitrum
    annotations:
      summary: "Low Arbitrum ETH balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) has a low ETH balance"

  - alert: LowOptimismBalance
    expr: native_balance{chain="optimism"} < *********000000000 # 0.1 ETH (18 decimals)
    for: 5m
    labels:
      severity: warning
      chain: optimism
    annotations:
      summary: "Low Optimism ETH balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) has a low ETH balance"

  - alert: LowBaseBalance
    expr: native_balance{chain="base"} < *********000000000 # 0.1 ETH (18 decimals)
    for: 5m
    labels:
      severity: warning
      chain: base
    annotations:
      summary: "Low Base ETH balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) has a low ETH balance"

  - alert: LowSolanaBalance
    expr: native_balance{chain="solana"} < ********* # 0.1 SOL (9 decimals)
    for: 5m
    labels:
      severity: warning
      chain: solana
    annotations:
      summary: "Low SOL balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) has a low SOL balance"

  # Token-specific alerts
  - alert: LowERC20USDTBalance
    expr: erc20_balance{token_address="******************************************"} < 1000000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low USDT balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) on {{ $labels.chain }} has a low USDT balance"

  - alert: LowERC20USDCBalance
    expr: erc20_balance{token_address="******************************************"} < 1000000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low USDC balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) on {{ $labels.chain }} has a low USDC balance"

  - alert: LowSPLUSDCBalance
    expr: spl_balance{token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"} < 1000000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low Solana USDC balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) on {{ $labels.chain }} has a low USDC balance"

  # Generic alerts as fallbacks
  - alert: LowERC20Balance
    expr: erc20_balance < 1000000 and erc20_balance{token_address!="******************************************"} and erc20_balance{token_address!="******************************************"}
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low ERC20 token balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) on {{ $labels.chain }} has a low balance for token {{ $labels.token_address }}"

  - alert: LowSPLBalance
    expr: spl_balance < 1000000 and spl_balance{token_address!="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low SPL token balance"
      description: "Account {{ $labels.account_name }} ({{ $labels.account }}) on {{ $labels.chain }} has a low balance for token {{ $labels.token_address }}"

  # Service health alerts
  - alert: HighFailureRate
    expr: sum(rate(check_failures_total[5m])) / sum(rate(check_failures_total[5m]) + rate(request_duration_seconds_count[5m])) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High balance check failure rate"
      description: "The balance monitor is experiencing a high failure rate (>10%)"