groups:
- name: test
  rules:
  - alert: AlwaysFiring
    expr: vector(1)
    for: 0s
    labels:
      severity: info
    annotations:
      summary: "Test Alert"
      description: "This is a test alert."
      
  - alert: TestLowBalance
    expr: vector(0.5) * on() group_left(account_name, chain) (
      label_replace(
        label_replace(
          vector(1),
          "account_name", "test-account", "", ""
        ),
        "chain", "ethereum", "", ""
      )
    )
    for: 0s
    labels:
      severity: warning
    annotations:
      summary: "Test Low Balance"
      description: "Test account on ethereum has low balance: 0.5" 